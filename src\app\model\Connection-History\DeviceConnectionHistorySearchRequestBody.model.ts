import { ConnectionTypeEnum } from "src/app/shared/enum/ConnectionHistory/ConnectionTypeEnum.enum";
import { OSTypeEnum } from "src/app/shared/enum/Probe/OSTypeEnum.enum";

export class DeviceConnectionHistorySearchRequestBody {
    manufacturer?: string;
    deviceModel?: string;
    deviceSerialNumber?: string;
    osType?: OSTypeEnum;
    lastConnectedDate?: number;

    constructor(manufacturer?: string, deviceModel?: string, deviceSerialNumber?: string, osType?: OSTypeEnum, lastConnectedDate?: number) {
        this.manufacturer = manufacturer;
        this.deviceModel = deviceModel;
        this.deviceSerialNumber = deviceSerialNumber;
        this.osType = osType;
        this.lastConnectedDate = lastConnectedDate;
    }
}