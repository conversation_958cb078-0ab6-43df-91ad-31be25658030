<!-- loading start -->
<!-- loading gif start -->
<div class="ringLoading" *ngIf="loading">
    <div class="ringLoadingDiv">
        <img src="assets/DoubleRing.gif" alt="loading" *ngIf="loading">
    </div>
</div>
<!-- loading gif end -->
<!-- loading end -->


<body *ngIf="deviceConnectionHistoryListDisplay">
    <!-- row start -->
    <div class="row">

        <!--############################################################-->
        <!--Filter start-->
        <!--############################################################-->
        <div class="col-md-3 pr-0" *ngIf="(!isFilterHidden)" id="deviceConnectionHistoryFilterBtn">
            <label class="col-md-12 h5-tag">Filter</label>
            <div class="card mt-3">
                <div class="card-body">
                    <app-device-connection-history-filter
                        [isFilterComponentInitWithApicall]="isFilterComponentInitWithApicall"
                        [deviceConnectionHistorySearchRequestBody]="deviceConnectionHistorySearchRequestBody"
                        [listPageRefreshForbackToDetailPage]="listPageRefreshForbackToDetailPage"></app-device-connection-history-filter>
                </div>
            </div>
        </div>
        <!--############################################################-->
        <!--Filter End-->
        <!--############################################################-->

        <!--table Block Start-->
        <div [className]="(isFilterHidden)?'col-md-12 pr-0':'col-md-9 pr-0'">
            <div class="container-fluid">
                <!--############################################################-->
                <!--############################################################-->
                <div class="row" class="headerAlignment">
                    <!--############################################################-->
                    <!--Left Side-->
                    <!--############################################################-->
                    <div class="childFlex">
                        <!----------------------------------------------->
                        <!------------Show/hide filter-------------------->
                        <!----------------------------------------------->
                        <div class="dropdown" id="hideShowFilter">
                            <button class="btn btn-sm btn-orange mr-3 ml-0 filter-symbol" (click)="toggleFilter()"
                                id="deviceConnectionHistoryListHideShowButton">
                                <i class="fas fa-filter" aria-hidden="true"></i>
                                &nbsp;&nbsp;{{ hideShowFilterButtonText }}
                            </button>
                        </div>
                        <!----------------------------------------------->
                        <!------------Pagnatation drp-------------------->
                        <!----------------------------------------------->
                        <div>
                            <label class="mb-0">Show entry</label>
                            <select [(ngModel)]="drpselectsize" class="form-control form-control-sm"
                                (change)="changeDataSize($event)" id="deviceConnectionHistoryListShowEntry">
                                <ng-template ngFor let-dataSize [ngForOf]="dataSizes">
                                    <option [value]="dataSize">{{ dataSize }}</option>
                                </ng-template>
                            </select>
                        </div>
                    </div>
                    <!--############################################################-->
                    <!--Right Side-->
                    <!--############################################################-->
                    <div class="childFlex">
                        <div class="btn-group btn-group-sm mr-2" role="group" style="display: inline-block;">
                            <button type="button" class="btn btn-sm btn-orange btn-cust-border">Device</button>
                            <button type="button" (click)="showProbeConnectionListDisplay()"
                                class="btn btn-sm btn-cust-border" id="deviceToProbe">Probe</button>
                        </div>

                        <!------------------------------------------------>
                        <!----------------refresh------------------------->
                        <!------------------------------------------------>
                        <div>
                            <button class="btn btn-sm btn-orange" (click)="clickOnRefreshButton()"
                                id="refresh_deviceConnectionHistoryList">
                                <em class="fa fa-refresh"></em>
                            </button>
                        </div>
                    </div>
                </div>
                <!--############################################################-->
                <!--############################################################-->
                <!-- selected Device Connection start -->
                <div>Total {{totalRecord}} Device Connection History(s)
                    <p
                        *ngIf="selectedDeviceConnectionHistoryIdList != null && selectedDeviceConnectionHistoryIdList.length > 0">
                        <strong>{{selectedDeviceConnectionHistoryIdList.length}} Device Connection History(s)
                            selected</strong>
                    </p>
                </div>
                <!-- selected probes end -->

                <!-------------------------------------------->
                <!-------------------------------------------->
                <!-- Device Connection History table start --->
                <!-------------------------------------------->
                <!-------------------------------------------->
                <div class="commonTable">
                    <table class="table table-sm table-bordered" style="overflow-x: scroll;" aria-hidden="true">
                        <!--###########################################-->
                        <!-- table header Start -->
                        <!--###########################################-->
                        <thead>
                            <tr class="thead-light">
                                <th class="checkox-table width-unset" *ngIf="showCheckBox">
                                    <div class="custom-control custom-checkbox">
                                        <input type="checkbox" class="custom-control-input" name="chkselectall"
                                            [id]="selectAllCheckboxId"
                                            (change)="selectAllItem($any($event.target)?.checked)">
                                        <label class="custom-control-label" [for]="selectAllCheckboxId"></label>
                                    </div>
                                </th>
                                <th><span class="text_nowrap">{{serialNumberAndHwId}}</span></th>
                                <th><span class="text_nowrap">{{deviceModel}}</span></th>
                                <th><span class="text_nowrap">{{manufacturer}}</span></th>
                                <th><span class="text_nowrap">{{osType}}</span></th>
                                <th><span class="text_nowrap">{{lastConnectedDate}}</span></th>
                            </tr>
                        </thead>
                        <!--###########################################-->
                        <!-- table body start -->
                        <!--###########################################-->
                        <tbody>
                            <tr *ngFor="let deviceConnectionHistoryObj of deviceConnectionHistoryResponseList;">
                                <td class="width-unset" *ngIf="showCheckBox">
                                    <div class="custom-control custom-checkbox">
                                        <input type="checkbox" class="custom-control-input"
                                            [id]="chkPreFix+deviceConnectionHistoryObj.id+chkPreFix"
                                            [name]="checkboxListName"
                                            (change)="selectCheckbox(deviceConnectionHistoryObj, $any($event.target).checked)"
                                            [checked]="selectedDeviceConnectionHistoryIdList.includes(deviceConnectionHistoryObj.id)">
                                        <label class="custom-control-label"
                                            [for]="chkPreFix+deviceConnectionHistoryObj.id+chkPreFix"></label>
                                    </div>
                                </td>
                                <td (click)="showDeviceConnectionHistoryDetail(deviceConnectionHistoryObj?.id)"
                                    class="spanunderline">
                                    <span class="text_nowrap">{{deviceConnectionHistoryObj?.deviceSerialNumber}}</span>
                                </td>
                                <td>
                                    <span class="text_nowrap">{{deviceConnectionHistoryObj?.deviceModel}}</span>
                                </td>
                                <td><span class="text_nowrap">{{deviceConnectionHistoryObj?.manufacturer}}</span>
                                </td>
                                <td><span class="text_nowrap">{{deviceConnectionHistoryObj?.osType}}</span></td>
                                <td><span class="text_nowrap">{{deviceConnectionHistoryObj?.lastConnectedDate |
                                        date:'MMM d, y, h:mm:ss a'}}</span></td>
                            </tr>
                        </tbody>
                        <!--###########################################-->
                        <!-- table body end -->
                        <!--###########################################-->
                    </table>

                </div>
                <!-------------------------------------------->
                <!-------------------------------------------->
                <!-- Device Connection History table End ----->
                <!-------------------------------------------->
                <!-------------------------------------------->

                <!----------------------------------------------------------->
                <!----------------------------------------------------------->
                <!--pagination Start-->
                <!----------------------------------------------------------->
                <!----------------------------------------------------------->
                <div>
                    <div>Showing {{totalRecordDisplay}} out of {{totalRecord}} Device Connection History(s)</div>
                    <div class="float-right">
                        <ngb-pagination [collectionSize]="totalItems" [(page)]="page" [pageSize]="itemsPerPage"
                            id="deviceConnectionHistoryList-pagination" [maxSize]="5" [rotate]="true"
                            [boundaryLinks]="true" (pageChange)="loadPage(page)">
                        </ngb-pagination>
                    </div>
                </div>
                <!----------------------------------------------------------->
                <!----------------------------------------------------------->
                <!--pagination end-->
                <!----------------------------------------------------------->
                <!----------------------------------------------------------->
            </div>
        </div>
        <!--table Block End-->
    </div>
    <!-- row end -->
</body>

<div *ngIf="deviceConnectionHistoryDetailDisplay">
    <app-device-connection-history-detail (showDeviceConnectionHistoryList)="showDeviceConnectionHistoryList()"
        [deviceConnectionHistoryId]="deviceConnectionHistoryId"></app-device-connection-history-detail>
</div>