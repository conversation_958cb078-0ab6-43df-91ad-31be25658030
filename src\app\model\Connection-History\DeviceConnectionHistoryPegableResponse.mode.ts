import { Pageable } from "../common/pageable.model";
import { PageResponse } from "../common/PageResponse.model";
import { Sort } from "../common/sort.model";
import { DeviceConnectionHistoryResponse } from "./DeviceConnectionHistoryResponse.model";

export class DeviceConnectionHistoryPegableResponse extends PageResponse {
    content: Array<DeviceConnectionHistoryResponse>;
    constructor( //NOSONAR
        pageable: Pageable,
        totalPages: number,
        last: boolean,
        totalElements: number,
        numberOfElements: number,
        first: boolean,
        sort: Sort,
        size: number,
        number: number,
        empty: boolean,
        content: Array<DeviceConnectionHistoryResponse>
    ) {
        super(
            pageable,
            totalPages,
            last,
            totalElements,
            numberOfElements,
            first,
            sort,
            size,
            number,
            empty
        );
        this.content = content;
    }
}
