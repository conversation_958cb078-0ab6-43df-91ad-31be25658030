import { Component, EventEmitter, OnInit, Output } from '@angular/core';
import { isUndefined } from 'is-what';
import { Subscription } from 'rxjs';
import { DEVICE_CONNECTION_HISTORY_DEVICE_MODEL, DEVICE_CONNECTION_HISTORY_LAST_CONNECTED_DATE, DEVICE_CONNECTION_HISTORY_MANUFACTURER, DEVICE_CONNECTION_HISTORY_OS_TYPE, DEVICE_CONNECTION_HISTORY_SERIAL_NUMBER, DeviceConnectionHistoryListResource, ITEMS_PER_PAGE } from 'src/app/app.constants';
import { DeviceConnectionHistoryResponse } from 'src/app/model/Connection-History/DeviceConnectionHistoryResponse.model';
import { DeviceConnectionHistorySearchRequestBody } from 'src/app/model/Connection-History/DeviceConnectionHistorySearchRequestBody.model';
import { AuthJwtService } from 'src/app/shared/auth-jwt.service';
import { collapseFilterTextEnum } from 'src/app/shared/enum/collapseFilterButtonText.enum';
import { PermissionAction } from 'src/app/shared/enum/Permission/permissionAction.enum';
import { PermissionService } from 'src/app/shared/permission.service';
import { CommonCheckboxService } from 'src/app/shared/util/common-checkbox.service';
import { CommonsService } from 'src/app/shared/util/commons.service';
import { DeviceConnectionHistoryOperationService } from '../Device-Connection-History-Service/Device-Connection-History-Operation/device-connection-history-operation-service.service';
import { DeviceConnectionHistoryFilterAction } from 'src/app/model/Connection-History/DeviceConnectionHistoryFilterAction.model';
import { ListingPageReloadSubjectParameter } from 'src/app/model/common/listingPageReloadSubjectParameter.model';

@Component({
  selector: 'app-device-connection-history-list',
  templateUrl: './device-connection-history-list.component.html',
  styleUrl: './device-connection-history-list.component.css'
})
export class DeviceConnectionHistoryListComponent implements OnInit {

  @Output('showprobeConnectionHistoryListDisplay') showprobeConnectionHistoryListDisplay = new EventEmitter();

  loading: boolean = false;

  //Page
  itemsPerPage: number = 0;
  page: number = 0;
  previousPage: number = 0;
  totalItems: number = 0;

  //Totel Count Display
  totalRecordDisplay: number = 0;
  totalRecord: number = 0;

  //Page Size DropDown
  dataSizes: string[] = [];
  drpselectsize: number = ITEMS_PER_PAGE;

  //Filter
  isFilterComponentInitWithApicall: boolean = true;
  listPageRefreshForbackToDetailPage: boolean = false;
  isFilterHidden: boolean = true;
  hideShowFilterButtonText: string = collapseFilterTextEnum.HIDE_FILTER;

  //Device connection Hostory List
  deviceConnectionHistoryResponseList: DeviceConnectionHistoryResponse[] = [];

  //Permission
  connectioHistoryAdminPermission: boolean = false;

  //subscription
  subscriptionForDeviceConnectionHistoryListFilterRequestParameter: Subscription;

  //Hide Show List and Detail Page
  deviceConnectionHistoryDetailDisplay: boolean = false;
  deviceConnectionHistoryListDisplay: boolean = false;
  deviceConnectionHistoryId: number = null;

  //unique CheckBox Name
  chkPreFix = "deviceConnectionHistory";
  selectAllCheckboxId = "selectAllDeviceConnection";
  checkboxListName = "deviceConnectionItem[]";

  //selected Device Connection Id Collect
  selectedDeviceConnectionHistoryIdList: number[] = [];
  localDeviceConnectionHistoryIdListArray: number[] = [];

  //Device Connection History serach request body store
  deviceConnectionHistorySearchRequestBody: DeviceConnectionHistorySearchRequestBody = null;

  //checkboxHide
  showCheckBox: boolean = true;

  //Constance
  serialNumberAndHwId: string = DEVICE_CONNECTION_HISTORY_SERIAL_NUMBER;
  deviceModel: string = DEVICE_CONNECTION_HISTORY_DEVICE_MODEL;
  manufacturer: string = DEVICE_CONNECTION_HISTORY_MANUFACTURER;
  osType: string = DEVICE_CONNECTION_HISTORY_OS_TYPE;
  lastConnectedDate: string = DEVICE_CONNECTION_HISTORY_LAST_CONNECTED_DATE;

  constructor(
    private authservice: AuthJwtService,
    private commonsService: CommonsService,
    private commonCheckboxService: CommonCheckboxService,
    private permissionService: PermissionService,
    private deviceConnectionHistoryOperationService: DeviceConnectionHistoryOperationService
  ) { }

  /**
  * <AUTHOR>
  */
  public ngOnInit(): void {
    if (!this.authservice.isAuthenticate()) {
      this.authservice.loginNavigate();
    } else {
      this.isFilterHidden = this.deviceConnectionHistoryOperationService.getIsFilterHiddenForListing();
      this.page = 0;
      this.dataSizes = this.commonsService.accessDataSizes();
      this.selectedDeviceConnectionHistoryIdList = [];
      this.isFilterComponentInitWithApicall = true;
      this.listPageRefreshForbackToDetailPage = false;
      this.deviceConnectionHistoryListDisplay = true;
      this.deviceConnectionHistoryDetailDisplay = false;
      this.setConnectionHistoryPermission();
      this.itemsPerPage = ITEMS_PER_PAGE;
      this.drpselectsize = ITEMS_PER_PAGE;
      this.previousPage = 1;

      // Initialize device connection history data if user has permission
      if (this.connectioHistoryAdminPermission) {
        this.getDeviceConnectionHistoryData();
      }
    }
    this.subjectInit();
  }

  /**
  * Permission set
  */
  private setConnectionHistoryPermission(): void {
    this.connectioHistoryAdminPermission = this.permissionService.getConnectionHistoryPermission(PermissionAction.GET_CONNECTION_HISTORY_ACTION);
  }

  /**
  * Initialize device connection history data
  * <AUTHOR>
  */
  private getDeviceConnectionHistoryData(): void {
    this.filterPageSubjectCallForReloadPage(true, true);
  }

  private subjectInit(): void {
    /**
    * This Subject call from Filter component
    * Load all the Data
    * <AUTHOR>
    */
    this.subscriptionForDeviceConnectionHistoryListFilterRequestParameter = this.deviceConnectionHistoryOperationService.getDeviceConnectionHistoryListFilterRequestParameterSubject()?.subscribe((deviceConnectionHistoryRequestParameter: DeviceConnectionHistoryFilterAction) => {
      if (deviceConnectionHistoryRequestParameter.listingPageReloadSubjectParameter.isReloadData) {
        if (deviceConnectionHistoryRequestParameter.listingPageReloadSubjectParameter.isDefaultPageNumber) {
          this.selectedDeviceConnectionHistoryIdList = [];
          this.resetPage()
        }
        this.loadAll(deviceConnectionHistoryRequestParameter.deviceConnectionHistoryBody);
      }
    });
  }

  /**
  * Destroy subscription
  * <AUTHOR>
  */
  public ngOnDestroy(): void {
    if (!isUndefined(this.subscriptionForDeviceConnectionHistoryListFilterRequestParameter)) { this.subscriptionForDeviceConnectionHistoryListFilterRequestParameter.unsubscribe() }
  }

  /**
  * Reset Page
  * <AUTHOR>
  */
  private resetPage(): void {
    this.page = 0;
    this.previousPage = 1;
  }

  /**
  * Clear all filter ,Reset Page and Reload the page
  * <AUTHOR>
  */
  public async refreshFilter(isClearFilter: boolean): Promise<void> {
    this.loading = true;
    this.resetPage();
    this.filterPageSubjectCallForReloadPage(true, isClearFilter);
  }

  /**
  * Item par page Value Changes like (10,50,100)
  * <AUTHOR>
  * @param datasize
  */
  public changeDataSize(datasize: any): void {
    this.setLoadingStatus(true);
    this.selectedDeviceConnectionHistoryIdList = [];
    this.itemsPerPage = datasize.target.value;
    this.filterPageSubjectCallForReloadPage(true, false);
  }

  /**
  * single Checkbox Select
  * <AUTHOR>
  * @param deviceConnectionHistoryObj 
  * @param isChecked 
  */
  public selectCheckbox(deviceConnectionHistoryObj: DeviceConnectionHistoryResponse, isChecked: boolean): void {
    if (isChecked) {
      this.selectedDeviceConnectionHistoryIdList.push(deviceConnectionHistoryObj.id);
    } else {
      let index = this.selectedDeviceConnectionHistoryIdList.findIndex(obj => obj == deviceConnectionHistoryObj.id);
      this.selectedDeviceConnectionHistoryIdList.splice(index, 1);
    }
    this.defaultSelectAll();
  }

  /**
  * select All checkbox select or deSelect
  * <AUTHOR>
  */
  private defaultSelectAll(): void {
    this.commonCheckboxService.defaultSelectAll(this.localDeviceConnectionHistoryIdListArray, this.selectedDeviceConnectionHistoryIdList, this.selectAllCheckboxId);
  }

  /**
  * Select All CheckBox
  * <AUTHOR>
  * @param isChecked 
  */
  public selectAllItem(isChecked: boolean): void {
    this.selectedDeviceConnectionHistoryIdList = this.commonCheckboxService.selectAllItem(isChecked, this.localDeviceConnectionHistoryIdListArray, this.selectedDeviceConnectionHistoryIdList, this.checkboxListName);
  }

  /**
  * Change The Page
  * callDeviceConnectionHistoryListRefreshSubject ->Call the filter component
  * filter not clear and send with filter requrest and load data
  * <AUTHOR>
  * @param page
  */
  public loadPage(page: any): void {
    if (page !== this.previousPage) {
      this.previousPage = page;
      this.commonCheckboxService.clearSelectAllCheckbox(this.selectAllCheckboxId);
      this.filterPageSubjectCallForReloadPage(false, false);
    }
  }

  /**
  * Call Filter component subject and reload page
  * <AUTHOR>
  * @param isDefaultPageNumber 
  * @param isClearFilter 
  */
  public filterPageSubjectCallForReloadPage(isDefaultPageNumber: boolean, isClearFilter: boolean): void {
    let listingPageReloadSubjectParameter = new ListingPageReloadSubjectParameter(true, isDefaultPageNumber, isClearFilter, false)
    this.deviceConnectionHistoryOperationService.callRefreshPageSubject(listingPageReloadSubjectParameter, DeviceConnectionHistoryListResource, this.isFilterHidden);
  }

  /**
  * Toggle Filter
  * <AUTHOR>
  * @param id 
  */
  public toggleFilter(): void {
    this.isFilterComponentInitWithApicall = false;
    this.listPageRefreshForbackToDetailPage = false;
    this.isFilterHidden = !this.isFilterHidden;
    if (this.isFilterHidden) {
      this.hideShowFilterButtonText = collapseFilterTextEnum.SHOW_FILTER;
    } else {
      this.hideShowFilterButtonText = collapseFilterTextEnum.HIDE_FILTER;
    }
  }

  /**
  * Device Connection History List API call using operation service
  *
  * <AUTHOR>
  * @param deviceConnectionHistorySearchRequestBody
  */
  public async loadAll(deviceConnectionHistorySearchRequestBody: DeviceConnectionHistorySearchRequestBody): Promise<void> {
    this.setLoadingStatus(true);
    this.deviceConnectionHistoryOperationService.setDeviceConnectionHistorySearchRequestBodyForListingApi(deviceConnectionHistorySearchRequestBody);

    const pageObj = {
      page: this.page - 1,
      size: this.itemsPerPage,
    };

    const result = await this.deviceConnectionHistoryOperationService.loadDeviceConnectionHistoryList(deviceConnectionHistorySearchRequestBody, pageObj);

    if (result.success) {
      this.deviceConnectionHistoryResponseList = result.deviceConnectionHistoryList;
      this.totalRecordDisplay = result.totalRecordDisplay;
      this.totalRecord = result.totalRecord;
      this.totalItems = result.totalItems;
      this.page = result.page;
      this.setLocalDeviceConnectionHistoryId(result.localDeviceConnectionHistoryList);
    } else {
      this.deviceConnectionHistoryResponseList = [];
      this.totalRecordDisplay = 0;
      this.totalRecord = 0;
      this.totalItems = 0;
    }

    this.setLoadingStatus(false);
    this.defaultSelectAll();
  }



  /**
  * Local Device Connection History list create for Select all Checkbox
  * <AUTHOR>
  * @param deviceConnectionHistoryIdList 
  */
  public setLocalDeviceConnectionHistoryId(deviceConnectionHistoryIdList: DeviceConnectionHistoryResponse[]): void {
    this.localDeviceConnectionHistoryIdListArray = [];
    for (let salesOrderObj of deviceConnectionHistoryIdList) {
      this.localDeviceConnectionHistoryIdListArray.push(salesOrderObj.id);
    }
    this.defaultSelectAll();
  }

  /**
  * Loading Status 
  * <AUTHOR>
  */
  private setLoadingStatus(status: boolean): void {
    this.loading = status;
  }


  /**
  * Show Device Connection History List 
  * 
  * <AUTHOR>
  */
  public showDeviceConnectionHistoryList(): void {
    this.isFilterComponentInitWithApicall = false;
    this.listPageRefreshForbackToDetailPage = true;
    this.deviceConnectionHistoryId = null;
    this.deviceConnectionHistoryListDisplay = true;
    this.deviceConnectionHistoryDetailDisplay = false;
    this.selectedDeviceConnectionHistoryIdList = [];
    if (this.isFilterHidden) {
      this.filterPageSubjectCallForReloadPage(true, false);
    }
  }

  /**
  * Show Device Connection History Detail
  * 
  * @param id 
  * 
  * <AUTHOR>
  */
  public showDeviceConnectionHistoryDetail(id: number): void {
    this.deviceConnectionHistoryId = id;
    this.deviceConnectionHistoryListDisplay = false;
    this.deviceConnectionHistoryDetailDisplay = true;
  }

  /**
  * Show Probe Connection History List Page
  * 
  * <AUTHOR>
  */
  public showProbeConnectionListDisplay(): void {
    this.showprobeConnectionHistoryListDisplay.emit();
  }

  /**
  * Refresh button click
  *
  * <AUTHOR>
  */
  public async clickOnRefreshButton(): Promise<void> {
    this.loading = true;
    this.resetPage();
    this.filterPageSubjectCallForReloadPage(true, false);
  }

}
