import { Injectable } from '@angular/core';
import { Subject } from 'rxjs';
import { DeviceConnectionHistoryDetailResource, DeviceConnectionHistoryListResource } from 'src/app/app.constants';
import { ListingPageReloadSubjectParameter } from 'src/app/model/common/listingPageReloadSubjectParameter.model';
import { DeviceConnectionHistoryFilterAction } from 'src/app/model/Connection-History/DeviceConnectionHistoryFilterAction.model';
import { DeviceConnectionHistorySearchRequestBody } from 'src/app/model/Connection-History/DeviceConnectionHistorySearchRequestBody.model';

@Injectable({
  providedIn: 'root'
})
export class DeviceConnectionHistoryOperationService {

  //Refresh device Connection History List 
  private deviceConnectionHistoryListRefreshSubject = new Subject<ListingPageReloadSubjectParameter>();

  //Refresh device Connection History Detail page
  private deviceConnectionHistoryDetailRefreshSubject = new Subject<ListingPageReloadSubjectParameter>();

  //device Connection History list filter
  private deviceConnectionHistoryListFilterRequestParameterSubject = new Subject<DeviceConnectionHistoryFilterAction>();

  /**
   * device Connection History List Page Refresh After some Action Like Serch parameter add
   * Note : Create or Update or Delete Role After Clear All filter and refresh page 
   * <AUTHOR>
   * @returns  
   */
  public getDeviceConnectionHistoryListFilterRequestParameterSubject(): Subject<DeviceConnectionHistoryFilterAction> {
    return this.deviceConnectionHistoryListFilterRequestParameterSubject;
  }

  public callDeviceConnectionHistoryListFilterRequestParameterSubject(kitManagemantFilterAction: DeviceConnectionHistoryFilterAction): void {
    this.deviceConnectionHistoryListFilterRequestParameterSubject.next(kitManagemantFilterAction);
  }

  /**
   * device Connection History List Page Refresh After some Action Like 
   * <AUTHOR>
   * @returns ListingPageReloadSubjectParameter
   */
  public getDeviceConnectionHistoryListRefreshSubject(): Subject<ListingPageReloadSubjectParameter> {
    return this.deviceConnectionHistoryListRefreshSubject;
  }

  /**
  * device Connection History Detail Page Refresh After some Action 
  * isReloadData false means delete Kit operation and move list page
  * <AUTHOR>
  * @returns ListingPageReloadSubjectParameter
  */
  public getDeviceConnectionHistoryDetailRefreshSubject(): Subject<ListingPageReloadSubjectParameter> {
    return this.deviceConnectionHistoryDetailRefreshSubject;
  }

  /**
  * This function call the subject for reload the page data
  *  Note : (DeviceConnectionHistoryListResource) -> Filter page subject call -> Listing page subject call
  * clear all filter after page data Reload
  * <AUTHOR>
  * @param isReloadData -> false means move to prev page form Detail to list page.
  * @param resourceName 
  */
  public callRefreshPageSubject(listingPageReloadSubjectParameter: ListingPageReloadSubjectParameter, resourceName: string, isFilterHidden: boolean): void {
    if (resourceName == DeviceConnectionHistoryListResource) {
      if (isFilterHidden) {
        let kitRequestBody = new DeviceConnectionHistorySearchRequestBody(null, null, null, null, null);
        let kitFilterAction = new DeviceConnectionHistoryFilterAction(listingPageReloadSubjectParameter, kitRequestBody);
        this.callDeviceConnectionHistoryListFilterRequestParameterSubject(kitFilterAction);
      } else {
        this.deviceConnectionHistoryListRefreshSubject.next(listingPageReloadSubjectParameter);
      }
    } else if (resourceName == DeviceConnectionHistoryDetailResource) {
      this.deviceConnectionHistoryDetailRefreshSubject.next(listingPageReloadSubjectParameter);
    }
  }

}
