import { HttpErrorResponse } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { isNullOrUndefined } from 'is-what';
import { ToastrService } from 'ngx-toastr';
import { Subject, firstValueFrom } from 'rxjs';
import {
  COMMON_SELECT_FILTER,
  DeviceConnectionHistoryDetailResource,
  DeviceConnectionHistoryListResource
} from 'src/app/app.constants';
import { ListingPageReloadSubjectParameter } from 'src/app/model/common/listingPageReloadSubjectParameter.model';
import { DeviceConnectionHistoryFilterAction } from 'src/app/model/Connection-History/DeviceConnectionHistoryFilterAction.model';
import { DeviceConnectionHistorySearchRequestBody } from 'src/app/model/Connection-History/DeviceConnectionHistorySearchRequestBody.model';
import { DeviceConnectionHistoryResponse } from 'src/app/model/Connection-History/DeviceConnectionHistoryResponse.model';
import { DeviceConnectionHistoryPegableResponse } from 'src/app/model/Connection-History/DeviceConnectionHistoryPegableResponse.mode';
import { ExceptionHandlingService } from 'src/app/shared/ExceptionHandling.service';
import { PermissionAction } from 'src/app/shared/enum/Permission/permissionAction.enum';
import { PermissionService } from 'src/app/shared/permission.service';
import { CommonsService } from 'src/app/shared/util/commons.service';
import { DeviceConnectionApiCallService } from '../Device-connection-Api-Call/device-connection-api-call.service';

/**
* Device Connection History List Result interface for consistent response handling
*/
export interface DeviceConnectionHistoryListResult {
  success: boolean;
  deviceConnectionHistoryList: DeviceConnectionHistoryResponse[];
  totalRecordDisplay: number;
  totalRecord: number;
  localDeviceConnectionHistoryList: DeviceConnectionHistoryResponse[];
  totalItems: number;
  page: number;
}

/**
* Device Connection History Operation Service for communication between filter and listing components
* Includes caching functionality to avoid unnecessary API calls on filter show/hide
* Follows the same pattern as probe module for consistency
*
* <AUTHOR>
*/
@Injectable({
  providedIn: 'root'
})
export class DeviceConnectionHistoryOperationService {

  constructor(
    private deviceConnectionApiCallService: DeviceConnectionApiCallService,
    private commonsService: CommonsService,
    private exceptionHandlingService: ExceptionHandlingService,
    private toastrService: ToastrService,
    private permissionService: PermissionService
  ) { }

  //Loading Status
  private deviceConnectionHistoryListLoadingSubject = new Subject<boolean>();
  private deviceConnectionHistoryDetailLoadingSubject = new Subject<boolean>();

  //Refresh device Connection History List
  private deviceConnectionHistoryListRefreshSubject = new Subject<ListingPageReloadSubjectParameter>();

  //Refresh device Connection History Detail page
  private deviceConnectionHistoryDetailRefreshSubject = new Subject<ListingPageReloadSubjectParameter>();

  //device Connection History list filter
  private deviceConnectionHistoryListFilterRequestParameterSubject = new Subject<DeviceConnectionHistoryFilterAction>();

  //Device Connection History Listing Page State Management
  deviceConnectionHistorySearchRequestBody: DeviceConnectionHistorySearchRequestBody = null;
  isFilterHidden: boolean = false;
  listPageRefreshForbackToOtherPage: boolean = false;

  /**
  * Device Connection History List Page Loading
  * <AUTHOR>
  * @returns Subject<boolean>
  */
  public getDeviceConnectionHistoryListLoadingSubject(): Subject<boolean> {
    return this.deviceConnectionHistoryListLoadingSubject;
  }

  public callDeviceConnectionHistoryListLoadingSubject(status: boolean): void {
    this.deviceConnectionHistoryListLoadingSubject.next(status);
  }

  /**
  * Device Connection History Detail Page Loading
  * <AUTHOR>
  * @returns Subject<boolean>
  */
  public getDeviceConnectionHistoryDetailLoadingSubject(): Subject<boolean> {
    return this.deviceConnectionHistoryDetailLoadingSubject;
  }

  public callDeviceConnectionHistoryDetailLoadingSubject(status: boolean): void {
    this.deviceConnectionHistoryDetailLoadingSubject.next(status);
  }

  /**
  * Device Connection History List Page Refresh After some Action
  * <AUTHOR>
  * @returns Subject<ListingPageReloadSubjectParameter>
  */
  public getDeviceConnectionHistoryListRefreshSubject(): Subject<ListingPageReloadSubjectParameter> {
    return this.deviceConnectionHistoryListRefreshSubject;
  }

  /**
  * Device Connection History Detail Page Refresh After some Action
  * isReloadData false means delete operation and move list page
  * <AUTHOR>
  * @returns Subject<ListingPageReloadSubjectParameter>
  */
  public getDeviceConnectionHistoryDetailRefreshSubject(): Subject<ListingPageReloadSubjectParameter> {
    return this.deviceConnectionHistoryDetailRefreshSubject;
  }

  /**
  * Get Device Connection History List Filter Request Parameter Subject
  * Used by listing component to subscribe to filter changes
  * <AUTHOR>
  * @returns Subject<DeviceConnectionHistoryFilterAction>
  */
  public getDeviceConnectionHistoryListFilterRequestParameterSubject(): Subject<DeviceConnectionHistoryFilterAction> {
    return this.deviceConnectionHistoryListFilterRequestParameterSubject;
  }

  /**
  * Set Device Connection History Search Request Body
  * <AUTHOR>
  * @param deviceConnectionHistorySearchRequestBody
  */
  public setDeviceConnectionHistorySearchRequestBodyForListingApi(deviceConnectionHistorySearchRequestBody: DeviceConnectionHistorySearchRequestBody) {
    this.deviceConnectionHistorySearchRequestBody = deviceConnectionHistorySearchRequestBody;
  }

  /**
  * Get Device Connection History Search Request Body
  * <AUTHOR>
  * @returns
  */
  public getDeviceConnectionHistorySearchRequestBodyForListingApi(): DeviceConnectionHistorySearchRequestBody {
    return this.deviceConnectionHistorySearchRequestBody;
  }

  /**
  * Set Filter Hide/Show
  * <AUTHOR>
  * @param isFilterHidden
  */
  public setIsFilterHiddenForListing(isFilterHidden: boolean) {
    this.isFilterHidden = isFilterHidden;
  }

  /**
  * Get Filter Hide/Show
  * <AUTHOR>
  * @returns boolean
  */
  public getIsFilterHiddenForListing(): boolean {
    return this.isFilterHidden;
  }

  /**
   * Set listPageRefreshForbackToOtherPage
   * <AUTHOR>
   * @param listPageRefreshForbackToOtherPage
   */
  public setListPageRefreshForbackToOtherPage(listPageRefreshForbackToOtherPage: boolean) {
    this.listPageRefreshForbackToOtherPage = listPageRefreshForbackToOtherPage;
  }

  /**
  * Get listPageRefreshForbackToOtherPage
  * <AUTHOR>
  * @returns boolean
  */
  public getListPageRefreshForbackToOtherPage(): boolean {
    return this.listPageRefreshForbackToOtherPage;
  }

  /**
  * Call Device Connection History List Filter Request Parameter Subject
  * Used by filter component to emit filter changes
  * <AUTHOR>
  * @param deviceConnectionHistoryFilterAction - The filter action containing search parameters
  */
  public callDeviceConnectionHistoryListFilterRequestParameterSubject(deviceConnectionHistoryFilterAction: DeviceConnectionHistoryFilterAction): void {
    this.deviceConnectionHistoryListFilterRequestParameterSubject.next(deviceConnectionHistoryFilterAction);
  }

  /**
  * This function call the subject for loading start and stop
  * <AUTHOR>
  * @param status
  * @param resourceName
  */
  public isLoading(status: boolean, resourceName: string): void {
    if (resourceName == DeviceConnectionHistoryListResource) {
      this.callDeviceConnectionHistoryListLoadingSubject(status);
    } else if (resourceName == DeviceConnectionHistoryDetailResource) {
      this.callDeviceConnectionHistoryDetailLoadingSubject(status);
    }
  }

  /**
  * This function call the subject for reload the page data
  * Note : (DeviceConnectionHistoryListResource) -> Filter page subject call -> Listing page subject call
  * clear all filter after page data Reload
  * <AUTHOR>
  * @param listingPageReloadSubjectParameter
  * @param resourceName
  * @param isFilterHidden
  * @param deviceConnectionHistorySearchRequestBodyApply
  */
  public callRefreshPageSubject(listingPageReloadSubjectParameter: ListingPageReloadSubjectParameter, resourceName: string, isFilterHidden: boolean, deviceConnectionHistorySearchRequestBodyApply?: DeviceConnectionHistorySearchRequestBody): void {
    if (resourceName == DeviceConnectionHistoryListResource) {
      if (isFilterHidden) {
        // Always use filter subject for list refresh (same pattern as probe service)
        let deviceConnectionHistorySearchRequestBody = new DeviceConnectionHistorySearchRequestBody(null, null, null, null, null);
        if (!isNullOrUndefined(deviceConnectionHistorySearchRequestBodyApply) && !listingPageReloadSubjectParameter.isClearFilter) {
          deviceConnectionHistorySearchRequestBody = deviceConnectionHistorySearchRequestBodyApply;
        }
        let deviceConnectionHistoryFilterAction = new DeviceConnectionHistoryFilterAction(listingPageReloadSubjectParameter, deviceConnectionHistorySearchRequestBody);
        this.callDeviceConnectionHistoryListFilterRequestParameterSubject(deviceConnectionHistoryFilterAction);
      } else {
        this.deviceConnectionHistoryListRefreshSubject.next(listingPageReloadSubjectParameter);
      }
    } else if (resourceName == DeviceConnectionHistoryDetailResource) {
      this.deviceConnectionHistoryDetailRefreshSubject.next(listingPageReloadSubjectParameter);
    }
  }

  /**
   * Call filter page subject for reload page
   * Used to maintain filter state when navigating between listing and detail pages
   * <AUTHOR>
   * @param isDefaultPageNumber - Whether to reset to default page number
   * @param isClearFilter - Whether to clear filters
   */
  public filterPageSubjectCallForReloadPage(isDefaultPageNumber: boolean, isClearFilter: boolean): void {
    const listingPageReloadSubjectParameter = new ListingPageReloadSubjectParameter(true, isDefaultPageNumber, isClearFilter, false);
    this.callRefreshPageSubject(listingPageReloadSubjectParameter, DeviceConnectionHistoryListResource, false, null);
  }

  /**
  * Load device connection history list with search parameters and pagination
  * Handles API call, response processing, and error handling
  * <AUTHOR>
  * @param deviceConnectionHistorySearchRequestBody - Search criteria for filtering
  * @param pageObj - Pagination parameters (page, size)
  * @returns Promise with device connection history list result
  */
  public async loadDeviceConnectionHistoryList(deviceConnectionHistorySearchRequestBody: DeviceConnectionHistorySearchRequestBody, pageObj: any): Promise<DeviceConnectionHistoryListResult> {
    try {
      if (this.permissionService.getConnectionHistoryPermission(PermissionAction.GET_CONNECTION_HISTORY_ACTION)) {
        const response = await firstValueFrom(this.deviceConnectionApiCallService.getDeviceConnectionHistoryList(deviceConnectionHistorySearchRequestBody, pageObj));

        if (response.status === 200 && response.body) {
          const deviceConnectionHistoryData = response.body;
          return {
            success: true,
            deviceConnectionHistoryList: deviceConnectionHistoryData.content,
            totalRecordDisplay: deviceConnectionHistoryData.numberOfElements,
            totalRecord: deviceConnectionHistoryData.totalElements,
            localDeviceConnectionHistoryList: deviceConnectionHistoryData.content,
            totalItems: deviceConnectionHistoryData.totalElements,
            page: deviceConnectionHistoryData.number + 1
          };
        } else {
          return this.getEmptyDeviceConnectionHistoryListResult();
        }
      } else {
        this.toastrService.error('Insufficient permissions to load device connection history list');
        return this.getEmptyDeviceConnectionHistoryListResult();
      }
    } catch (error) {
      this.exceptionHandlingService.customErrorMessage(error as HttpErrorResponse);
      return this.getEmptyDeviceConnectionHistoryListResult();
    }
  }

  /**
  * Get empty device connection history list result for error cases
  * <AUTHOR>
  * @returns DeviceConnectionHistoryListResult
  */
  private getEmptyDeviceConnectionHistoryListResult(): DeviceConnectionHistoryListResult {
    return {
      success: false,
      deviceConnectionHistoryList: [],
      totalRecordDisplay: 0,
      totalRecord: 0,
      localDeviceConnectionHistoryList: [],
      totalItems: 0,
      page: 0
    };
  }

  /**
  * Process filter search and validate form
  * <AUTHOR>
  * @param formValue - Form values from filter component
  * @param isFormInvalid - Whether form is invalid
  * @param defaultListingPageReloadSubjectParameter - Default reload parameters
  * @returns boolean indicating if search should proceed
  */
  public processFilterSearch(formValue: any, isFormInvalid: boolean, defaultListingPageReloadSubjectParameter: ListingPageReloadSubjectParameter): boolean {
    if (isFormInvalid) {
      return false;
    }

    if (!this.validateDeviceConnectionHistoryFilterForm(formValue)) {
      return false;
    }

    const deviceConnectionHistorySearchRequestBody = this.buildDeviceConnectionHistoryFilterRequestBody(formValue);
    const deviceConnectionHistoryFilterAction = new DeviceConnectionHistoryFilterAction(defaultListingPageReloadSubjectParameter, deviceConnectionHistorySearchRequestBody);
    this.callDeviceConnectionHistoryListFilterRequestParameterSubject(deviceConnectionHistoryFilterAction);
    return true;
  }

  /**
  * Validate device connection history filter form
  * <AUTHOR>
  * @param formValue - Form values to validate
  * @returns boolean indicating if validation passed
  */
  public validateDeviceConnectionHistoryFilterForm(formValue: any): boolean {
    const {
      serialNumber, deviceModel, manufacturer, osType, lastConnectedDateAndTime
    } = formValue;

    const hasAnyFilter = serialNumber || deviceModel || manufacturer ||
      osType?.length || lastConnectedDateAndTime;

    if (!hasAnyFilter) {
      this.toastrService.info(COMMON_SELECT_FILTER);
      return false;
    }

    return true;
  }

  /**
  * Build device connection history filter request body from form values
  * <AUTHOR>
  * @param formValue - Form values from filter component
  * @returns DeviceConnectionHistorySearchRequestBody
  */
  public buildDeviceConnectionHistoryFilterRequestBody(formValue: any): DeviceConnectionHistorySearchRequestBody {
    const manufacturer = this.commonsService.checkNullFieldValue(formValue.manufacturer);
    const deviceModel = this.commonsService.checkNullFieldValue(formValue.deviceModel);
    const serialNumber = this.commonsService.checkNullFieldValue(formValue.serialNumber);
    const osType = this.commonsService.getSelectedValueFromEnum(formValue.osType);
    const lastConnectedDateAndTime = this.commonsService.checkNullFieldValue(formValue.lastConnectedDateAndTime);

    return new DeviceConnectionHistorySearchRequestBody(
      manufacturer,
      deviceModel,
      serialNumber,
      osType ? osType[0] : null,
      lastConnectedDateAndTime
    );
  }

  /**
  * Clear all filters and refresh the listing
  * <AUTHOR>
  * @param listingPageReloadSubjectParameter - Reload parameters
  */
  public clearAllFiltersAndRefresh(listingPageReloadSubjectParameter: ListingPageReloadSubjectParameter): void {
    const emptyFilterRequestBody = new DeviceConnectionHistorySearchRequestBody(null, null, null, null, null);
    const deviceConnectionHistoryFilterAction = new DeviceConnectionHistoryFilterAction(listingPageReloadSubjectParameter, emptyFilterRequestBody);
    this.callDeviceConnectionHistoryListFilterRequestParameterSubject(deviceConnectionHistoryFilterAction);
  }

}
