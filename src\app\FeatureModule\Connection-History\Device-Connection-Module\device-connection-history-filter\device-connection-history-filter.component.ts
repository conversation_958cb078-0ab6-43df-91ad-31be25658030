import { Component, Input } from '@angular/core';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { isUndefined } from 'is-what';
import { Subscription } from 'rxjs';
import { DEVICE_CONNECTION_HISTORY_DEVICE_MODEL, DEVICE_CONNECTION_HISTORY_LAST_CONNECTED_DATE, DEVICE_CONNECTION_HISTORY_MANUFACTURER, DEVICE_CONNECTION_HISTORY_OS_TYPE, DEVICE_CONNECTION_HISTORY_SERIAL_NUMBER, FILTER_CLEAR_BUTTON, FILTER_SEARCH_BUTTON, SMALL_TEXTBOX_MAX_CHARACTERS_ALLOWED_MESSAGE, SMALL_TEXTBOX_MAX_LENGTH, SPECIAL_CHARACTER_ERROR_MESSAGE, SPECIAL_CHARACTER_PATTERN } from 'src/app/app.constants';
import { ListingPageReloadSubjectParameter } from 'src/app/model/common/listingPageReloadSubjectParameter.model';
import { DeviceConnectionHistoryFilterAction } from 'src/app/model/Connection-History/DeviceConnectionHistoryFilterAction.model';
import { DeviceConnectionHistorySearchRequestBody } from 'src/app/model/Connection-History/DeviceConnectionHistorySearchRequestBody.model';
import { MultiSelectDropdownSettings } from 'src/app/model/MultiSelectDropdownSettings.model';
import { OSTypeEnum } from 'src/app/shared/enum/Probe/OSTypeEnum.enum';
import { MultiSelectDropDownSettingService } from 'src/app/shared/util/multi-select-drop-down-setting.service';
import { DeviceConnectionHistoryOperationService } from '../Device-Connection-History-Service/Device-Connection-History-Operation/device-connection-history-operation-service.service';
import { ValidationService } from 'src/app/shared/util/validation.service';
import { CommonsService } from 'src/app/shared/util/commons.service';
import { CommonOperationsService } from 'src/app/shared/util/common-operations.service';
import { KeyValueMappingServiceService } from 'src/app/shared/util/key-value-mapping-service.service';
import { EnumMapping } from 'src/app/model/common/EnumMapping.model';

@Component({
  selector: 'app-device-connection-history-filter',
  templateUrl: './device-connection-history-filter.component.html',
  styleUrl: './device-connection-history-filter.component.css'
})
export class DeviceConnectionHistoryFilterComponent {
  @Input("isFilterComponentInitWithApicall") isFilterComponentInitWithApicall: boolean;
  @Input("listPageRefreshForbackToDetailPage") listPageRefreshForbackToDetailPage: boolean;
  @Input("deviceConnectionHistorySearchRequestBody") deviceConnectionHistorySearchRequestBody: DeviceConnectionHistorySearchRequestBody;

  //MaxLength Message
  textBoxMaxLengthMessage: string = SMALL_TEXTBOX_MAX_CHARACTERS_ALLOWED_MESSAGE;
  specialCharacterErrorMessage: string = SPECIAL_CHARACTER_ERROR_MESSAGE;

  //maxDate
  maxdate: Date = new Date();

  //Constance
  serialNumberAndHwId: string = DEVICE_CONNECTION_HISTORY_SERIAL_NUMBER;
  deviceModel: string = DEVICE_CONNECTION_HISTORY_DEVICE_MODEL;
  manufacturer: string = DEVICE_CONNECTION_HISTORY_MANUFACTURER;
  osType: string = DEVICE_CONNECTION_HISTORY_OS_TYPE;
  lastConnectedDate: string = DEVICE_CONNECTION_HISTORY_LAST_CONNECTED_DATE;
  searchBtnText: string = FILTER_SEARCH_BUTTON;
  clearBtnText: string = FILTER_CLEAR_BUTTON;



  filterDeviceConnectionHistoryForm = new FormGroup({
    serialNumber: new FormControl(null, [Validators.maxLength(SMALL_TEXTBOX_MAX_LENGTH), this.validationService.removeSpaces, Validators.pattern(SPECIAL_CHARACTER_PATTERN)]),
    deviceModel: new FormControl(null, [Validators.maxLength(SMALL_TEXTBOX_MAX_LENGTH), this.validationService.removeSpaces, Validators.pattern(SPECIAL_CHARACTER_PATTERN)]),
    manufacturer: new FormControl(null, [Validators.maxLength(SMALL_TEXTBOX_MAX_LENGTH), this.validationService.removeSpaces, Validators.pattern(SPECIAL_CHARACTER_PATTERN)]),
    osType: new FormControl([], []),
    lastConnectedDateAndTime: new FormControl(null, []),
  });

  subscriptionForRefeshList: Subscription;

  defaultListingPageReloadSubjectParameter = new ListingPageReloadSubjectParameter(true, true, false, false);

  dropdownSettingsForOsType: MultiSelectDropdownSettings = null;

  osTypeList: Array<EnumMapping> = [];

  constructor(private multiSelectDropDownSettingService: MultiSelectDropDownSettingService,
    private deviceConnectionHistoryService: DeviceConnectionHistoryOperationService,
    private commonsService: CommonsService,
    private commonOperationsService: CommonOperationsService,
    private validationService: ValidationService,
    private keyValueMappingServiceService: KeyValueMappingServiceService,
  ) { }


  /**
  * On Init 
  * 
  * <AUTHOR>
  */
  public ngOnInit(): void {
    this.dropdownSettingsForOsType = this.multiSelectDropDownSettingService.getOTSTypeDrpSetting();
    this.onInitSubject();
    this.getFilterList();
    if (this.isFilterComponentInitWithApicall) {
      this.clearFilter(this.defaultListingPageReloadSubjectParameter);
    }
  }

  /**
   * Destroy 
   */
  public ngOnDestroy(): void {
    if (!isUndefined(this.subscriptionForRefeshList)) { this.subscriptionForRefeshList.unsubscribe() }
  }

  /**
  * Subject 
  * 
  * <AUTHOR>
  */
  public onInitSubject(): void {
    /**
    * Device Connection History Refresh After some Action 
    * <AUTHOR>
    */
    this.subscriptionForRefeshList = this.deviceConnectionHistoryService.getDeviceConnectionHistoryListRefreshSubject().subscribe((listingPageReloadSubjectParameter: ListingPageReloadSubjectParameter) => {
      if (listingPageReloadSubjectParameter.isReloadData) {
        if (listingPageReloadSubjectParameter.isClearFilter) {
          this.clearFilter(listingPageReloadSubjectParameter);
        } else {
          //page change 1,2,3
          this.deviceConnectionHistoryListPageRefresh(listingPageReloadSubjectParameter);
        }
      }
    });
  }


  /**
  * Get Filter Value List
  *
  * <AUTHOR>
  */
  public async getFilterList(): Promise<void> {
    this.osTypeList = this.keyValueMappingServiceService.enumOptionToList(OSTypeEnum);
    this.setFilterValue();
  }

  /**
   * set Filter value
   */
  private setFilterValue() {
    if (this.deviceConnectionHistorySearchRequestBody != null) {
      this.filterDeviceConnectionHistoryForm.get('serialNumber').setValue(this.deviceConnectionHistorySearchRequestBody.deviceSerialNumber);
      this.filterDeviceConnectionHistoryForm.get('deviceModel').setValue(this.deviceConnectionHistorySearchRequestBody.deviceModel);
      this.filterDeviceConnectionHistoryForm.get('manufacturer').setValue(this.deviceConnectionHistorySearchRequestBody.manufacturer);
      const osType = this.deviceConnectionHistorySearchRequestBody.osType ?
        this.commonsService.getEnumMappingSelectedValue(OSTypeEnum, [this.deviceConnectionHistorySearchRequestBody.osType]) : [];
      this.filterDeviceConnectionHistoryForm.get('osType').setValue(osType);
      this.filterDeviceConnectionHistoryForm.get('lastConnectedDateAndTime').setValue(this.deviceConnectionHistorySearchRequestBody.lastConnectedDate);
    }
    if (this.listPageRefreshForbackToDetailPage) {
      this.deviceConnectionHistoryListPageRefresh(this.defaultListingPageReloadSubjectParameter);
    }
  }


  /**
   * Search Data
   * 
   * <AUTHOR>
   */
  public searchData(): void {
    let allFormValue = this.filterDeviceConnectionHistoryForm.value;
    if (this.filterDeviceConnectionHistoryForm.invalid || (this.commonsService.checkValueIsNullOrEmpty(allFormValue.serialNumber) &&
      this.commonsService.checkValueIsNullOrEmpty(allFormValue.deviceModel) &&
      this.commonsService.checkValueIsNullOrEmpty(allFormValue.manufacturer) &&
      this.commonsService.checkValueIsNullOrEmpty(allFormValue.lastConnectedDateAndTime) &&
      allFormValue.osType.length)) {
      this.commonOperationsService.showEmptyFilterTosteMessge();
    } else {
      this.deviceConnectionHistoryListPageRefresh(this.defaultListingPageReloadSubjectParameter);
    }
  }

  /**
   * Clear All filter and serach api call
   * 
   * <AUTHOR>
   * 
   * @param listingPageReloadSubjectParameter 
   */
  public clearFilter(listingPageReloadSubjectParameter: ListingPageReloadSubjectParameter): void {
    this.filterDeviceConnectionHistoryForm.get('serialNumber').setValue(null);
    this.filterDeviceConnectionHistoryForm.get('deviceModel').setValue(null);
    this.filterDeviceConnectionHistoryForm.get('manufacturer').setValue(null);
    this.filterDeviceConnectionHistoryForm.get('lastConnectedDateAndTime').setValue(null);
    this.filterDeviceConnectionHistoryForm.get('osType').setValue([]);
    this.deviceConnectionHistoryListPageRefresh(listingPageReloadSubjectParameter);
  }

  /**
   * Device Connection History List Page Serch api call
   * 
   * <AUTHOR>
   * 
   * @param listingPageReloadSubjectParameter 
   */
  private deviceConnectionHistoryListPageRefresh(listingPageReloadSubjectParameter: ListingPageReloadSubjectParameter): void {
    if (this.filterDeviceConnectionHistoryForm.invalid) {
      this.filterDeviceConnectionHistoryForm.reset();
    }
    let allFormValue = this.filterDeviceConnectionHistoryForm.value;
    const osType = this.commonsService.getSelectedValueFromEnum(allFormValue.osType);
    let deviceConnectionHistoryRequestBody = new DeviceConnectionHistorySearchRequestBody(
      this.commonsService.checkNullFieldValue(allFormValue.manufacturer),
      this.commonsService.checkNullFieldValue(allFormValue.deviceModel),
      this.commonsService.checkNullFieldValue(allFormValue.serialNumber),
      osType ? osType[0] : null,
      this.commonsService.checkNullFieldValue(allFormValue.lastConnectedDateAndTime));
    let deviceConnectionHistoryFilterAction = new DeviceConnectionHistoryFilterAction(listingPageReloadSubjectParameter, deviceConnectionHistoryRequestBody);
    this.deviceConnectionHistoryService.callDeviceConnectionHistoryListFilterRequestParameterSubject(deviceConnectionHistoryFilterAction);
  }
}
