import { HttpClient, HttpResponse } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { catchError, Observable } from 'rxjs';
import { DeviceConnectionHistoryPegableResponse } from 'src/app/model/Connection-History/DeviceConnectionHistoryPegableResponse.mode';
import { DeviceConnectionHistorySearchRequestBody } from 'src/app/model/Connection-History/DeviceConnectionHistorySearchRequestBody.model';
import { ExceptionHandlingService } from 'src/app/shared/ExceptionHandling.service';
import { ConfigInjectService } from 'src/app/shared/InjectService/config-inject.service';
import { CommonsService } from 'src/app/shared/util/commons.service';
import { createRequestOption } from 'src/app/shared/util/request-util';

@Injectable({
  providedIn: 'root'
})
export class DeviceConnectionApiCallService {

  public deviceConnectionHistory = this.configInjectService.getServerApiUrl() + 'api/connection-history';

  constructor(
    private configInjectService: ConfigInjectService,
    private http: HttpClient,
    private exceptionService: ExceptionHandlingService,
    private commonsService: CommonsService,
  ) { }

  /**
  * Get Sales Order list
  * @param requestBody 
  * @param req 
  * @returns 
  */
  public getDeviceConnectionHistoryList(requestBody: DeviceConnectionHistorySearchRequestBody, req: any): Observable<HttpResponse<DeviceConnectionHistoryPegableResponse>> {
    const options = createRequestOption(req);
    return this.http.post<DeviceConnectionHistoryPegableResponse>(this.deviceConnectionHistory + "/devices/search", requestBody, { params: options, observe: 'response' }).pipe(catchError(this.commonsService.handleError));
  }
}
